#!/usr/bin/env python3
"""
Script Principal para Calibração Unificada de TODAS as Métricas QUALIA
Executa simulação histórica unificada para determinar thresholds ótimos

MELHORIAS ESTRUTURAIS IMPLEMENTADAS:
✅ Cache inteligente para dados históricos e resultados
✅ Processamento paralelo de múltiplos ativos
✅ Validação cruzada e métricas de qualidade
✅ Regime-aware calibration com janelas múltiplas
✅ Target-driven search otimizado
✅ Sistema de monitoramento integrado

Métricas calibradas:
- consciousness, coherence, confidence
- volume_surge_min, momentum_min

Uso:
    python scripts/calibrate_all_qualia_metrics.py

Opções avançadas:
    python scripts/calibrate_all_qualia_metrics.py --days 45 --profit 0.025 --horizon 6 --parallel --validation
"""

import asyncio
import sys
import os
import argparse
import json
from pathlib import Path
from datetime import datetime
import numpy as np
import pandas as pd

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from qualia.binance_system import QualiaBinanceCorrectedSystem
from qualia.geometric_metrics_calibrator import QualiaMetricsCalibrator
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

def parse_arguments():
    """Parse argumentos da linha de comando"""
    parser = argparse.ArgumentParser(
        description="Calibracao Unificada de Metricas QUALIA",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos:
  calibrate_all_qualia_metrics.py                    # Calibração padrão (30 dias, 2%, 4h)
  calibrate_all_qualia_metrics.py --days 45          # 45 dias de histórico
  calibrate_all_qualia_metrics.py --profit 0.025     # 2.5 de lucro mínimo
  calibrate_all_qualia_metrics.py --horizon 6        # 6 horas de horizonte
  calibrate_all_qualia_metrics.py --gradual 0.5      # Aplicação 50 gradual
  calibrate_all_qualia_metrics.py --no-apply         # Não aplicar, apenas analisar
        """
    )
    
    parser.add_argument('--days', type=int, default=30,
                       help='Dias de histórico para análise (padrão: 30)')
    parser.add_argument('--profit', type=float, default=0.02,
                       help='Threshold de lucro para sucesso (padrão: 0.02 = 2%)')
    parser.add_argument('--horizon', type=int, default=4,
                       help='Horizonte temporal em horas (padrão: 4)')
    parser.add_argument('--gradual', type=float, default=0.7,
                       help='Fator de aplicação gradual (padrão: 0.7 = 70%)')
    parser.add_argument('--no-apply', action='store_true',
                       help='Não aplicar thresholds, apenas analisar')
    parser.add_argument('--save-detailed', action='store_true',
                       help='Salvar relatório detalhado em JSON')
    parser.add_argument('--quiet', action='store_true',
                       help='Modo silencioso (menos output)')

    # Novas opções estruturais
    parser.add_argument('--no-cache', action='store_true',
                       help='Desabilitar cache inteligente')
    parser.add_argument('--no-parallel', action='store_true',
                       help='Desabilitar processamento paralelo')
    parser.add_argument('--no-validation', action='store_true',
                       help='Desabilitar validação cruzada')
    parser.add_argument('--regime-aware', action='store_true', default=True,
                       help='Usar calibração regime-aware (padrão: ativado)')
    parser.add_argument('--fast-window', type=int, default=3,
                       help='Janela rápida para regime-aware (dias)')
    parser.add_argument('--slow-window', type=int, default=30,
                       help='Janela lenta para regime-aware (dias)')

    return parser.parse_args()

async def main():
    """Função principal de calibração unificada"""
    args = parse_arguments()
    
    if not args.quiet:
        print("QUALIA - Calibracao Unificada de TODAS as Metricas")
        print("=" * 70)
        print("Metricas: consciousness, coherence, confidence, volume_surge, momentum")
        print("=" * 70)
        print(f"Parametros: {args.days} dias | {args.profit*100:.1f}% lucro | {args.horizon}h horizonte")
        print("=" * 70)
    
    try:
        # 1. Inicializar sistema de trading
        logger.info("Inicializando sistema QUALIA...")
        trading_system = QualiaBinanceCorrectedSystem()

        # Inicializar conexão com Binance
        connected = await trading_system.initialize_binance_connection()
        if not connected:
            logger.warning("Nao foi possivel conectar com Binance, continuando em modo simulacao")
        
        # 2. Criar calibrador unificado com melhorias estruturais
        calibrator = QualiaMetricsCalibrator(
            trading_system=trading_system,
            enable_cache=not args.no_cache,
            enable_parallel=not args.no_parallel,
            enable_validation=not args.no_validation
        )

        # 3. Executar calibração unificada com melhorias estruturais
        logger.info("Iniciando calibração UNIFICADA baseada em simulação histórica...")
        logger.info("Objetivo: Otimizar TODOS os thresholds simultaneamente")
        # logger.info("IMPORTANTE: Obtendo dados históricos REAIS da API Binance...")

        if not args.quiet:
            print(f"\n MELHORIAS ESTRUTURAIS ATIVADAS:")
            print(f"   Cache inteligente: {'✅' if not args.no_cache else '❌'}")
            print(f"   Processamento paralelo: {'✅' if not args.no_parallel else '❌'}")
            print(f"   Validação cruzada: {'✅' if not args.no_validation else '❌'}")
            print(f"   Regime-aware calibration: {'✅' if args.regime_aware else '❌'}")

        results = await calibrator.calibrate_all_assets(
            days_back=args.days,
            profit_threshold=args.profit,
            time_horizon_hours=args.horizon,
            regime_aware=args.regime_aware,
            fast_window_days=args.fast_window,
            slow_window_days=args.slow_window,
            enable_validation=not args.no_validation,
            enable_parallel=not args.no_parallel
        )
        
        # 4. Mostrar resumo
        if not args.quiet:
            summary = calibrator.get_calibration_summary(results)
            print(summary)
        
        # 5. Gerar relatório detalhado se solicitado
        detailed_report = None
        if args.save_detailed:
            detailed_report = calibrator.generate_detailed_report(results)
            await save_detailed_report(detailed_report, args)
        
        # 6. Mostrar análise detalhada
        if not args.quiet:
            print("\n" + "="*70)
            print(" ANÁLISE DETALHADA POR MÉTRICA")
            print("="*70)
            await show_detailed_analysis(results)
        
        # 7. Aplicar thresholds se solicitado
        if not args.no_apply:
            if not args.quiet:
                print("\n" + "="*70)
                response = input(" Aplicar TODOS os thresholds calibrados ao sistema? (s/N): ").lower()
            else:
                response = 's'  # Auto-aplicar em modo silencioso
            
            if response in ['s', 'sim', 'y', 'yes']:
                await calibrator.apply_calibrated_thresholds(results, args.gradual)

                if not args.quiet:
                    print(" TODOS os thresholds aplicados com sucesso!")
                    print(" TRADING MODES atualizados automaticamente!")
                    await show_final_thresholds(trading_system)
                    await show_trading_modes_summary(trading_system)
                
            else:
                print("ℹ Thresholds não aplicados. Resultados salvos para análise.")
        else:
            logger.info("Modo --no-apply: Thresholds nao aplicados")
        
        # 8. Mostrar próximos passos
        if not args.quiet:
            print("\nPROXIMOS PASSOS:")
            print("1. Monitorar performance com novos thresholds")
            print("2. Executar recalibracao semanalmente")
            print("3. Ajustar baseado em trades reais")
            print("4. Analisar correlacao entre metricas")
            print("5. Implementar calibracao online adaptativa")
        
        return results
        
    except Exception as e:
        logger.error(f"Erro durante calibração: {e}")
        raise
    
    finally:
        # Cleanup (sistema nao tem metodo cleanup especifico)
        if 'trading_system' in locals():
            logger.info("Finalizando sistema...")

async def save_detailed_report(report, args):
    """Salva relatório detalhado em JSON"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"qualia_calibration_detailed_{timestamp}.json"
    filepath = Path('data/calibration') / filename
    
    # Criar diretório se não existir
    filepath.parent.mkdir(parents=True, exist_ok=True)
    
    # Adicionar parametros da calibracao
    report['calibration_parameters'] = {
        'days_back': args.days,
        'profit_threshold': args.profit,
        'time_horizon_hours': args.horizon,
        'gradual_factor': args.gradual
    }
    
    with open(filepath, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    logger.info(f" Relatório detalhado salvo em: {filepath}")

async def show_detailed_analysis(results):
    """Mostra análise detalhada dos resultados por métrica"""
    if not results:
        print(" Nenhum resultado disponível")
        return
    
    # Calcular estatísticas agregadas por métrica
    all_metrics = ['consciousness', 'coherence', 'confidence', 'volume_surge_min',
                   'momentum_min']
    
    for metric in all_metrics:
        values = []
        for result in results.values():
            if metric in result.recommended_thresholds:
                values.append(result.recommended_thresholds[metric])
        
        if values:
            print(f"\n {metric.upper().replace('_', ' ')}:")
            print(f"   Ativos:   {len(values)}")
            print(f"   Mínimo:   {min(values):.3f}")
            print(f"   Máximo:   {max(values):.3f}")
            print(f"   Mediana:  {np.median(values):.3f}")
            print(f"   Média:    {np.mean(values):.3f}")
            print(f"   Desvio:   {np.std(values):.3f}")
            
            # Mostrar distribuição
            q25, q75 = np.percentile(values, [25, 75])
            print(f"   Q25-Q75:  {q25:.3f} - {q75:.3f}")

async def show_final_thresholds(trading_system):
    """Mostra thresholds finais aplicados ao sistema"""
    print("\n THRESHOLDS FINAIS APLICADOS:")
    print("-" * 50)
    
    thresholds = trading_system.quantum_thresholds
    
    # Organizar por categoria
    main_metrics = ['consciousness', 'coherence', 'confidence']
    volume_momentum = ['volume_surge_min', 'momentum_min']
    
    print("METRICAS PRINCIPAIS:")
    for metric in main_metrics:
        if metric in thresholds:
            print(f"   {metric}: {thresholds[metric]:.3f}")

    print("\nVOLUME & MOMENTUM:")
    for metric in volume_momentum:
        if metric in thresholds:
            print(f"   {metric}: {thresholds[metric]:.3f}")

    print("\nMETRICAS GEOMETRICAS:")
    for metric in geometric:
        if metric in thresholds:
            print(f"   {metric}: {thresholds[metric]:.3f}")

async def show_trading_modes_summary(trading_system):
    """Mostra resumo dos trading modes atualizados"""
    print("\n TRADING MODES CALIBRADOS:")
    print("=" * 50)

    # Tentar obter do adaptive_manager primeiro
    if hasattr(trading_system, 'adaptive_manager') and trading_system.adaptive_manager:
        from qualia.adaptive_threshold_system import TradingMode

        for mode in [TradingMode.CONSERVATIVE, TradingMode.MODERATE, TradingMode.AGGRESSIVE]:
            config = trading_system.adaptive_manager.threshold_configs.get(mode)
            if config:
                print(f"\n{mode.value.upper()}:")
                print(f"   consciousness: {config.consciousness:.3f}")
                print(f"   coherence:     {config.coherence:.3f}")
                print(f"   confidence:    {config.confidence:.3f}")
                print(f"   volume_surge:  {config.volume_surge_min:.3f}")
                print(f"   momentum:      {config.momentum_min:.3f}")
    else:
        print("   AdaptiveThresholdManager nao disponivel")

def run_calibration():
    """Wrapper para executar calibração unificada"""
    try:
        return asyncio.run(main())
    except KeyboardInterrupt:
        print("\n Calibração interrompida pelo usuário")
        return None
    except Exception as e:
        print(f"\n Erro fatal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Importar pandas aqui para evitar erro se não estiver disponível
    try:
        import pandas as pd
    except ImportError:
        print(" pandas não encontrado. Instale com: pip install pandas")
        sys.exit(1)
    
    run_calibration()
