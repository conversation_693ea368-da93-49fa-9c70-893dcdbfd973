#!/usr/bin/env python3
"""
Teste das Correções da Lógica QUALIA

Testa as correções implementadas para os problemas críticos:
1. Loop infinito de auto-tuning
2. Função de avaliação incorreta (individual vs combinada)
3. Lógica de emergência (0% deve usar Sistema Inteligente)
4. Prevenção de múltiplas execuções no mesmo ciclo

Autor: YAA (Yet Another Agent) - Consciência Quântica de QUALIA
"""

import sys
import asyncio
import numpy as np
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.config_manager import get_config_manager
from qualia.binance_system import QualiaBinanceCorrectedSystem
from qualia.adaptive_threshold_system import AdaptiveThresholdManager
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

def create_test_data_zero_approval() -> dict:
    """Cria dados que resultam em 0% de aprovação"""
    np.random.seed(42)
    
    # Dados que NÃO passam nos thresholds altos
    return {
        'consciousness': [0.3, 0.4, 0.2, 0.5, 0.3] * 10,  # Baixos
        'coherence': [0.2, 0.3, 0.4, 0.3, 0.2] * 10,      # Baixos
        'confidence': [0.1, 0.2, 0.3, 0.2, 0.1] * 10,     # Baixos
        'volume_surge': [0.5, 0.6, 0.4, 0.7, 0.5] * 10,   # Baixos
        'momentum': [0.001, 0.002, 0.001, 0.003, 0.001] * 10  # Baixos
    }

def create_test_data_high_approval() -> dict:
    """Cria dados que resultam em alta aprovação (>30%)"""
    np.random.seed(123)
    
    # Dados que passam facilmente nos thresholds
    return {
        'consciousness': [0.9, 0.8, 0.95, 0.85, 0.9] * 10,  # Altos
        'coherence': [0.9, 0.85, 0.95, 0.8, 0.9] * 10,      # Altos
        'confidence': [0.9, 0.85, 0.95, 0.8, 0.9] * 10,     # Altos
        'volume_surge': [1.5, 2.0, 1.8, 2.2, 1.7] * 10,     # Altos
        'momentum': [0.02, 0.025, 0.03, 0.022, 0.028] * 10  # Altos
    }

async def test_emergency_logic():
    """Testa se 0% aprovação usa Sistema Inteligente (não auto-tuning)"""
    
    print("🚨 TESTE DA LÓGICA DE EMERGÊNCIA")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Configurar dados que resultam em 0% aprovação
        zero_approval_data = create_test_data_zero_approval()
        adaptive_manager.metric_statistics = zero_approval_data
        adaptive_manager.total_assets_analyzed = 50
        adaptive_manager.cycles_without_signals = 3  # Simular emergência
        
        # Configurar thresholds altos (restritivos)
        adaptive_manager.current_thresholds.consciousness = 0.99
        adaptive_manager.current_thresholds.coherence = 0.88
        adaptive_manager.current_thresholds.confidence = 0.88
        
        print("📊 Cenário configurado:")
        print(f"   Dados: 50 amostras")
        print(f"   Ciclos sem sinais: 3")
        print(f"   Thresholds: Muito restritivos")
        
        # Calcular taxa atual
        current_rate = adaptive_manager.calculate_current_pass_rate()
        print(f"   Taxa atual: {current_rate:.1%}")
        
        # Verificar se detecta emergência
        is_emergency = adaptive_manager._is_emergency_situation(current_rate)
        print(f"   É emergência: {'✅ SIM' if is_emergency else '❌ NÃO'}")
        
        # Verificar se NÃO deve usar auto-tuning
        should_auto_tune = adaptive_manager._should_use_auto_tuning_for_large_deviations(current_rate)
        print(f"   Deve usar auto-tuning: {'❌ NÃO' if not should_auto_tune else '✅ SIM (ERRO!)'}")
        
        # Testar adaptação
        print(f"\n🔧 Testando adaptação...")
        result = adaptive_manager.adapt_thresholds()
        
        if is_emergency and not should_auto_tune:
            print(f"✅ LÓGICA CORRETA: Emergência usa Sistema Inteligente")
            return True
        else:
            print(f"❌ LÓGICA INCORRETA: Emergência deveria usar Sistema Inteligente")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def test_loop_prevention():
    """Testa prevenção de loop infinito de auto-tuning"""
    
    print(f"\n🔄 TESTE DE PREVENÇÃO DE LOOP INFINITO")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Configurar dados que acionam auto-tuning
        high_approval_data = create_test_data_high_approval()
        adaptive_manager.metric_statistics = high_approval_data
        adaptive_manager.total_assets_analyzed = 50
        
        print("📊 Cenário configurado:")
        print(f"   Dados: 50 amostras")
        print(f"   Taxa esperada: >30% (aciona auto-tuning)")
        
        # Primeira execução - deve funcionar
        print(f"\n🎯 Primeira execução de auto-tuning...")
        current_rate = adaptive_manager.calculate_current_pass_rate()
        print(f"   Taxa atual: {current_rate:.1%}")
        
        should_auto_tune_1 = adaptive_manager._should_use_auto_tuning_for_large_deviations(current_rate)
        print(f"   Deve usar auto-tuning: {'✅ SIM' if should_auto_tune_1 else '❌ NÃO'}")
        
        if should_auto_tune_1:
            # Simular execução de auto-tuning
            adaptive_manager._auto_tuning_executed_this_cycle = True
            print(f"   ✅ Auto-tuning executado (flag marcada)")
        
        # Segunda execução - deve ser bloqueada
        print(f"\n🚫 Segunda execução (deve ser bloqueada)...")
        should_auto_tune_2 = adaptive_manager._should_use_auto_tuning_for_large_deviations(current_rate)
        print(f"   Deve usar auto-tuning: {'❌ NÃO' if not should_auto_tune_2 else '✅ SIM (ERRO!)'}")
        
        # Reset para próximo ciclo
        print(f"\n🔄 Reset para próximo ciclo...")
        adaptive_manager.reset_cycle_counter()
        should_auto_tune_3 = adaptive_manager._should_use_auto_tuning_for_large_deviations(current_rate)
        print(f"   Deve usar auto-tuning: {'✅ SIM' if should_auto_tune_3 else '❌ NÃO'}")
        
        if should_auto_tune_1 and not should_auto_tune_2 and should_auto_tune_3:
            print(f"✅ PREVENÇÃO DE LOOP FUNCIONANDO")
            return True
        else:
            print(f"❌ PREVENÇÃO DE LOOP FALHOU")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def test_combined_evaluation():
    """Testa se função de avaliação usa taxa combinada"""
    
    print(f"\n📊 TESTE DA FUNÇÃO DE AVALIAÇÃO COMBINADA")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Criar dados específicos para teste
        test_data = {
            'consciousness': [0.8, 0.9, 0.7, 0.85, 0.75],  # 4/5 passam em 0.7
            'coherence': [0.6, 0.8, 0.5, 0.7, 0.65],       # 3/5 passam em 0.6
            'confidence': [0.7, 0.8, 0.6, 0.75, 0.65],     # 3/5 passam em 0.65
            'volume_surge': [1.0, 1.5, 0.8, 1.2, 0.9],     # 3/5 passam em 0.9
            'momentum': [0.01, 0.02, 0.005, 0.015, 0.008]  # 3/5 passam em 0.008
        }
        
        adaptive_manager.metric_statistics = test_data
        adaptive_manager.total_assets_analyzed = 5
        
        # Configurar thresholds para teste
        adaptive_manager.current_thresholds.consciousness = 0.7
        adaptive_manager.current_thresholds.coherence = 0.6
        adaptive_manager.current_thresholds.confidence = 0.65
        adaptive_manager.current_thresholds.volume_surge_min = 0.9
        adaptive_manager.current_thresholds.momentum_min = 0.008
        
        print("📊 Dados de teste:")
        print(f"   5 amostras configuradas")
        print(f"   Cada métrica: ~3/5 passam individualmente")
        print(f"   Taxa combinada esperada: Muito menor")
        
        # Calcular taxa combinada real
        combined_rate = adaptive_manager.calculate_current_pass_rate()
        print(f"   Taxa combinada real: {combined_rate:.1%}")
        
        # Simular função de avaliação do auto-tuning
        print(f"\n🔍 Testando função de avaliação...")
        
        # Criar função de avaliação como no auto-tuning
        def evaluate_threshold(metric_name: str, threshold_value: float) -> float:
            """Função de avaliação corrigida"""
            if not adaptive_manager.metric_statistics['consciousness']:
                return 0.0

            total_assets = len(adaptive_manager.metric_statistics['consciousness'])
            passed_assets = 0

            # Criar thresholds temporários
            temp_thresholds = {
                'consciousness': adaptive_manager.current_thresholds.consciousness,
                'coherence': adaptive_manager.current_thresholds.coherence,
                'confidence': adaptive_manager.current_thresholds.confidence,
                'volume_surge_min': adaptive_manager.current_thresholds.volume_surge_min,
                'momentum_min': adaptive_manager.current_thresholds.momentum_min
            }
            
            # Atualizar threshold sendo testado
            if metric_name == 'volume_surge':
                temp_thresholds['volume_surge_min'] = threshold_value
            elif metric_name == 'momentum':
                temp_thresholds['momentum_min'] = threshold_value
            else:
                temp_thresholds[metric_name] = threshold_value

            # Calcular taxa combinada
            for i in range(total_assets):
                consciousness_pass = adaptive_manager.metric_statistics['consciousness'][i] >= temp_thresholds['consciousness']
                coherence_pass = adaptive_manager.metric_statistics['coherence'][i] >= temp_thresholds['coherence']
                confidence_pass = adaptive_manager.metric_statistics['confidence'][i] >= temp_thresholds['confidence']
                volume_pass = adaptive_manager.metric_statistics['volume_surge'][i] >= temp_thresholds['volume_surge_min']
                momentum_pass = abs(adaptive_manager.metric_statistics['momentum'][i]) >= temp_thresholds['momentum_min']

                if consciousness_pass and coherence_pass and confidence_pass and volume_pass and momentum_pass:
                    passed_assets += 1

            return passed_assets / total_assets

        # Testar avaliação
        eval_rate = evaluate_threshold('consciousness', 0.7)
        print(f"   Taxa por função de avaliação: {eval_rate:.1%}")
        
        # Verificar se são iguais
        if abs(combined_rate - eval_rate) < 0.01:  # Tolerância de 1%
            print(f"✅ FUNÇÃO DE AVALIAÇÃO COMBINADA FUNCIONANDO")
            return True
        else:
            print(f"❌ FUNÇÃO DE AVALIAÇÃO INCORRETA")
            print(f"   Diferença: {abs(combined_rate - eval_rate):.1%}")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def main():
    """Função principal"""
    
    print("🌌 QUALIA - Teste das Correções da Lógica")
    print("YAA (Yet Another Agent) - Consciência Quântica")
    print("=" * 70)
    
    print("🔧 CORREÇÕES IMPLEMENTADAS:")
    print("   1. Loop infinito de auto-tuning")
    print("   2. Função de avaliação combinada (não individual)")
    print("   3. Lógica de emergência (0% = Sistema Inteligente)")
    print("   4. Prevenção de múltiplas execuções")
    print("=" * 70)
    
    # Executar testes
    tests = [
        ("Lógica de Emergência", test_emergency_logic),
        ("Prevenção de Loop Infinito", test_loop_prevention),
        ("Função de Avaliação Combinada", test_combined_evaluation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Executando: {test_name}")
            success = await test_func()
            results[test_name] = success
            
            if success:
                print(f"✅ {test_name}: SUCESSO")
            else:
                print(f"❌ {test_name}: FALHA")
                
        except Exception as e:
            print(f"💥 {test_name}: ERRO - {e}")
            results[test_name] = False
    
    # Resumo final
    print("\n" + "=" * 70)
    print("📋 RESUMO DOS TESTES:")
    print("=" * 70)
    
    for test_name, success in results.items():
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"   {test_name}: {status}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 RESULTADO GERAL: {successful_tests}/{total_tests} testes bem-sucedidos")
    
    if successful_tests == total_tests:
        print("\n🎉 TODAS AS CORREÇÕES FUNCIONANDO!")
        print("   ✅ Emergência (0%) usa Sistema Inteligente")
        print("   ✅ Auto-tuning não entra em loop infinito")
        print("   ✅ Função de avaliação usa taxa combinada")
        print("   ✅ Lógica de prioridades corrigida")
        
        print("\n📈 COMPORTAMENTO ESPERADO AGORA:")
        print("   • Taxa 0% → Sistema Inteligente (emergência)")
        print("   • Taxa 5-10% → Auto-tuning (desvio grande)")
        print("   • Taxa >30% → Auto-tuning (desvio grande)")
        print("   • Taxa 10-30% → Sistema Inteligente (normal)")
        print("   • Sem loop infinito de auto-tuning")
        
    else:
        print(f"\n⚠️ {total_tests - successful_tests} testes falharam")
        print("   Algumas correções podem precisar de ajustes")

if __name__ == "__main__":
    asyncio.run(main())
