#!/usr/bin/env python3
"""
Sistema de Cache Inteligente para Calibração QUALIA

Responsabilidades:
- Cache de dados históricos por ativo
- Cache de resultados de calibração
- Invalidação inteligente baseada em tempo
- Compressão e persistência eficiente
- Métricas de hit/miss do cache

Autor: YAA (Yet Another Agent) - Consciência Quântica de QUALIA
"""

import json
import pickle
import hashlib
import time
import gzip
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict

from ..utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class CacheEntry:
    """Entrada do cache com metadados"""
    key: str
    data: Any
    timestamp: float
    size_bytes: int
    access_count: int
    last_access: float
    ttl_seconds: int
    compressed: bool = False

@dataclass
class CacheStats:
    """Estatísticas do cache"""
    total_entries: int
    total_size_mb: float
    hit_rate: float
    miss_rate: float
    avg_access_time_ms: float
    oldest_entry_age_hours: float
    compression_ratio: float

class IntelligentCacheManager:
    """
    Gerenciador de cache inteligente para dados de calibração
    
    Funcionalidades:
    - Cache hierárquico (memória + disco)
    - Compressão automática para dados grandes
    - Invalidação baseada em TTL e LRU
    - Métricas detalhadas de performance
    - Persistência automática
    """
    
    def __init__(self, cache_dir: str = "data/calibration_cache", max_memory_mb: int = 512):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache em memória
        self.memory_cache: Dict[str, CacheEntry] = {}
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.current_memory_usage = 0
        
        # Estatísticas
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'compressions': 0,
            'access_times': []
        }
        
        # Configurações de TTL por tipo de dados
        self.ttl_config = {
            'historical_data': 3600 * 6,      # 6 horas
            'calibration_results': 3600 * 24, # 24 horas
            'market_regime': 3600,             # 1 hora
            'momentum_history': 3600 * 2,      # 2 horas
            'percentile_analysis': 3600 * 12   # 12 horas
        }
        
        logger.info(f"Cache inteligente inicializado: {cache_dir}, max_memory={max_memory_mb}MB")

    def _generate_cache_key(self, data_type: str, **kwargs) -> str:
        """Gera chave única para o cache"""
        # Criar string determinística dos parâmetros
        params_str = json.dumps(kwargs, sort_keys=True)
        hash_obj = hashlib.md5(params_str.encode())
        return f"{data_type}_{hash_obj.hexdigest()[:12]}"

    def _should_compress(self, data: Any) -> bool:
        """Determina se os dados devem ser comprimidos"""
        try:
            # Estimar tamanho
            if isinstance(data, (list, dict)):
                return len(str(data)) > 10000  # > 10KB
            elif hasattr(data, '__len__'):
                return len(data) > 1000
            return False
        except:
            return False

    def _compress_data(self, data: Any) -> bytes:
        """Comprime dados usando gzip"""
        try:
            pickled = pickle.dumps(data)
            compressed = gzip.compress(pickled)
            self.stats['compressions'] += 1
            return compressed
        except Exception as e:
            logger.warning(f"Falha na compressão: {e}")
            return pickle.dumps(data)

    def _decompress_data(self, compressed_data: bytes, is_compressed: bool) -> Any:
        """Descomprime dados"""
        try:
            if is_compressed:
                decompressed = gzip.decompress(compressed_data)
                return pickle.loads(decompressed)
            else:
                return pickle.loads(compressed_data)
        except Exception as e:
            logger.error(f"Falha na descompressão: {e}")
            raise

    def _evict_lru_entries(self, required_bytes: int):
        """Remove entradas menos recentemente usadas"""
        if not self.memory_cache:
            return
        
        # Ordenar por último acesso
        sorted_entries = sorted(
            self.memory_cache.items(),
            key=lambda x: x[1].last_access
        )
        
        freed_bytes = 0
        for key, entry in sorted_entries:
            if freed_bytes >= required_bytes:
                break
            
            # Persistir no disco antes de remover
            self._persist_to_disk(key, entry)
            
            # Remover da memória
            del self.memory_cache[key]
            self.current_memory_usage -= entry.size_bytes
            freed_bytes += entry.size_bytes
            self.stats['evictions'] += 1
            
            logger.debug(f"Cache eviction: {key} ({entry.size_bytes} bytes)")

    def _persist_to_disk(self, key: str, entry: CacheEntry):
        """Persiste entrada no disco"""
        try:
            disk_file = self.cache_dir / f"{key}.cache"
            
            # Preparar dados para persistência
            persist_data = {
                'data': entry.data,
                'timestamp': entry.timestamp,
                'ttl_seconds': entry.ttl_seconds,
                'compressed': entry.compressed,
                'access_count': entry.access_count
            }
            
            # Salvar comprimido se necessário
            if entry.compressed or self._should_compress(persist_data):
                with open(disk_file, 'wb') as f:
                    compressed = self._compress_data(persist_data)
                    f.write(compressed)
                persist_data['compressed'] = True
            else:
                with open(disk_file, 'wb') as f:
                    pickle.dump(persist_data, f)
                    
        except Exception as e:
            logger.warning(f"Falha persistindo {key}: {e}")

    def _load_from_disk(self, key: str) -> Optional[CacheEntry]:
        """Carrega entrada do disco"""
        try:
            disk_file = self.cache_dir / f"{key}.cache"
            if not disk_file.exists():
                return None
            
            # Carregar dados
            with open(disk_file, 'rb') as f:
                file_data = f.read()
            
            # Tentar descomprimir primeiro
            try:
                persist_data = self._decompress_data(file_data, True)
            except:
                # Se falhar, tentar pickle normal
                persist_data = pickle.loads(file_data)
            
            # Verificar TTL
            age = time.time() - persist_data['timestamp']
            if age > persist_data['ttl_seconds']:
                disk_file.unlink()  # Remover arquivo expirado
                return None
            
            # Criar entrada
            entry = CacheEntry(
                key=key,
                data=persist_data['data'],
                timestamp=persist_data['timestamp'],
                size_bytes=len(file_data),
                access_count=persist_data.get('access_count', 0),
                last_access=time.time(),
                ttl_seconds=persist_data['ttl_seconds'],
                compressed=persist_data.get('compressed', False)
            )
            
            return entry
            
        except Exception as e:
            logger.warning(f"Falha carregando {key} do disco: {e}")
            return None

    def get(self, data_type: str, **kwargs) -> Optional[Any]:
        """Obtém dados do cache"""
        start_time = time.time()
        key = self._generate_cache_key(data_type, **kwargs)
        
        # Verificar cache em memória
        if key in self.memory_cache:
            entry = self.memory_cache[key]
            
            # Verificar TTL
            age = time.time() - entry.timestamp
            if age > entry.ttl_seconds:
                del self.memory_cache[key]
                self.current_memory_usage -= entry.size_bytes
            else:
                # Hit no cache
                entry.access_count += 1
                entry.last_access = time.time()
                self.stats['hits'] += 1
                
                access_time = (time.time() - start_time) * 1000
                self.stats['access_times'].append(access_time)
                
                logger.debug(f"Cache hit (memory): {key}")
                return entry.data
        
        # Verificar cache em disco
        entry = self._load_from_disk(key)
        if entry:
            # Carregar de volta na memória se houver espaço
            if self.current_memory_usage + entry.size_bytes <= self.max_memory_bytes:
                self.memory_cache[key] = entry
                self.current_memory_usage += entry.size_bytes
            
            self.stats['hits'] += 1
            access_time = (time.time() - start_time) * 1000
            self.stats['access_times'].append(access_time)
            
            logger.debug(f"Cache hit (disk): {key}")
            return entry.data
        
        # Miss no cache
        self.stats['misses'] += 1
        access_time = (time.time() - start_time) * 1000
        self.stats['access_times'].append(access_time)
        
        logger.debug(f"Cache miss: {key}")
        return None

    def put(self, data_type: str, data: Any, **kwargs):
        """Armazena dados no cache"""
        key = self._generate_cache_key(data_type, **kwargs)
        
        # Determinar TTL
        ttl = self.ttl_config.get(data_type, 3600)  # Default 1 hora
        
        # Calcular tamanho
        should_compress = self._should_compress(data)
        if should_compress:
            data_bytes = self._compress_data(data)
            size_bytes = len(data_bytes)
        else:
            data_bytes = pickle.dumps(data)
            size_bytes = len(data_bytes)
        
        # Verificar se precisa fazer eviction
        if self.current_memory_usage + size_bytes > self.max_memory_bytes:
            self._evict_lru_entries(size_bytes)
        
        # Criar entrada
        entry = CacheEntry(
            key=key,
            data=data,
            timestamp=time.time(),
            size_bytes=size_bytes,
            access_count=1,
            last_access=time.time(),
            ttl_seconds=ttl,
            compressed=should_compress
        )
        
        # Armazenar na memória
        self.memory_cache[key] = entry
        self.current_memory_usage += size_bytes
        
        logger.debug(f"Cache put: {key} ({size_bytes} bytes, TTL={ttl}s)")

    def invalidate(self, data_type: str, **kwargs):
        """Invalida entrada específica do cache"""
        key = self._generate_cache_key(data_type, **kwargs)
        
        # Remover da memória
        if key in self.memory_cache:
            entry = self.memory_cache[key]
            del self.memory_cache[key]
            self.current_memory_usage -= entry.size_bytes
        
        # Remover do disco
        disk_file = self.cache_dir / f"{key}.cache"
        if disk_file.exists():
            disk_file.unlink()
        
        logger.debug(f"Cache invalidated: {key}")

    def clear_expired(self):
        """Remove todas as entradas expiradas"""
        current_time = time.time()
        expired_keys = []
        
        # Verificar cache em memória
        for key, entry in self.memory_cache.items():
            if current_time - entry.timestamp > entry.ttl_seconds:
                expired_keys.append(key)
        
        # Remover expiradas da memória
        for key in expired_keys:
            entry = self.memory_cache[key]
            del self.memory_cache[key]
            self.current_memory_usage -= entry.size_bytes
        
        # Verificar arquivos em disco
        for cache_file in self.cache_dir.glob("*.cache"):
            try:
                # Verificar idade do arquivo
                file_age = current_time - cache_file.stat().st_mtime
                if file_age > 86400:  # > 24 horas
                    cache_file.unlink()
            except:
                pass
        
        if expired_keys:
            logger.info(f"Cache cleanup: {len(expired_keys)} entradas expiradas removidas")

    def get_stats(self) -> CacheStats:
        """Retorna estatísticas do cache"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0
        miss_rate = 1 - hit_rate
        
        avg_access_time = (
            sum(self.stats['access_times']) / len(self.stats['access_times'])
            if self.stats['access_times'] else 0
        )
        
        # Calcular idade da entrada mais antiga
        oldest_age = 0
        if self.memory_cache:
            oldest_timestamp = min(entry.timestamp for entry in self.memory_cache.values())
            oldest_age = (time.time() - oldest_timestamp) / 3600
        
        # Calcular ratio de compressão
        compressed_entries = sum(1 for entry in self.memory_cache.values() if entry.compressed)
        compression_ratio = compressed_entries / len(self.memory_cache) if self.memory_cache else 0
        
        return CacheStats(
            total_entries=len(self.memory_cache),
            total_size_mb=self.current_memory_usage / (1024 * 1024),
            hit_rate=hit_rate,
            miss_rate=miss_rate,
            avg_access_time_ms=avg_access_time,
            oldest_entry_age_hours=oldest_age,
            compression_ratio=compression_ratio
        )
