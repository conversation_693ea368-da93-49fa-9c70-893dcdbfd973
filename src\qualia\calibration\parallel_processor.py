#!/usr/bin/env python3
"""
Sistema de Processamento Paralelo para Calibração QUALIA

Responsabilidades:
- Calibração paralela de múltiplos ativos
- Pool de workers otimizado
- Balanceamento de carga inteligente
- Monitoramento de progresso em tempo real
- Tratamento robusto de erros

Autor: YAA (Yet Another Agent) - Consciência Quântica de QUALIA
"""

import asyncio
import concurrent.futures
import time
import psutil
from typing import Dict, List, Callable, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from ..utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class WorkerTask:
    """Tarefa para worker"""
    task_id: str
    symbol: str
    function: Callable
    args: tuple
    kwargs: dict
    priority: int = 0
    estimated_duration: float = 60.0  # segundos

@dataclass
class TaskResult:
    """Resultado de uma tarefa"""
    task_id: str
    symbol: str
    success: bool
    result: Any
    error: Optional[str]
    duration: float
    worker_id: int

@dataclass
class WorkerStats:
    """Estatísticas de um worker"""
    worker_id: int
    tasks_completed: int
    total_duration: float
    avg_duration: float
    errors: int
    current_task: Optional[str]
    cpu_usage: float
    memory_usage_mb: float

class ParallelCalibrationProcessor:
    """
    Processador paralelo otimizado para calibração de ativos
    
    Funcionalidades:
    - Pool de workers adaptativos
    - Balanceamento de carga por complexidade
    - Monitoramento de recursos do sistema
    - Retry automático para falhas
    - Métricas detalhadas de performance
    """
    
    def __init__(self, max_workers: Optional[int] = None, enable_monitoring: bool = True):
        # Determinar número ótimo de workers
        if max_workers is None:
            cpu_count = psutil.cpu_count(logical=False)  # Cores físicos
            max_workers = min(cpu_count * 2, 8)  # Máximo 8 workers
        
        self.max_workers = max_workers
        self.enable_monitoring = enable_monitoring
        
        # Pool de workers
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        
        # Filas de tarefas
        self.task_queue: asyncio.Queue = asyncio.Queue()
        self.result_queue: asyncio.Queue = asyncio.Queue()
        
        # Estado dos workers
        self.worker_stats: Dict[int, WorkerStats] = {}
        self.active_tasks: Dict[str, WorkerTask] = {}
        
        # Estatísticas globais
        self.global_stats = {
            'tasks_submitted': 0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'total_duration': 0.0,
            'start_time': time.time()
        }
        
        # Configurações
        self.retry_attempts = 2
        self.timeout_seconds = 300  # 5 minutos por tarefa
        
        logger.info(f"Processador paralelo inicializado: {max_workers} workers")

    async def submit_calibration_batch(self, calibration_tasks: List[WorkerTask]) -> Dict[str, TaskResult]:
        """
        Submete lote de tarefas de calibração para processamento paralelo
        
        Args:
            calibration_tasks: Lista de tarefas de calibração
            
        Returns:
            Dicionário com resultados por símbolo
        """
        logger.info(f"Iniciando processamento paralelo de {len(calibration_tasks)} tarefas")
        
        # Ordenar tarefas por prioridade e duração estimada
        sorted_tasks = sorted(
            calibration_tasks,
            key=lambda t: (t.priority, t.estimated_duration),
            reverse=True
        )
        
        # Submeter tarefas
        futures = {}
        for task in sorted_tasks:
            future = self.executor.submit(self._execute_task, task)
            futures[task.task_id] = future
            self.global_stats['tasks_submitted'] += 1
        
        # Coletar resultados
        results = {}
        completed = 0
        
        # Monitoramento de progresso
        if self.enable_monitoring:
            monitor_task = asyncio.create_task(self._monitor_progress(len(calibration_tasks)))
        
        try:
            for task_id, future in futures.items():
                try:
                    # Aguardar resultado com timeout
                    result = await asyncio.wait_for(
                        asyncio.wrap_future(future),
                        timeout=self.timeout_seconds
                    )
                    
                    results[result.symbol] = result
                    completed += 1
                    
                    if result.success:
                        self.global_stats['tasks_completed'] += 1
                    else:
                        self.global_stats['tasks_failed'] += 1
                    
                    self.global_stats['total_duration'] += result.duration
                    
                    # Log de progresso
                    progress = (completed / len(calibration_tasks)) * 100
                    logger.info(f"Progresso: {completed}/{len(calibration_tasks)} ({progress:.1f}%) - "
                              f"{result.symbol}: {'✅' if result.success else '❌'}")
                    
                except asyncio.TimeoutError:
                    logger.error(f"Timeout na tarefa {task_id}")
                    results[task_id] = TaskResult(
                        task_id=task_id,
                        symbol="UNKNOWN",
                        success=False,
                        result=None,
                        error="Timeout",
                        duration=self.timeout_seconds,
                        worker_id=-1
                    )
                    self.global_stats['tasks_failed'] += 1
                
                except Exception as e:
                    logger.error(f"Erro na tarefa {task_id}: {e}")
                    results[task_id] = TaskResult(
                        task_id=task_id,
                        symbol="UNKNOWN",
                        success=False,
                        result=None,
                        error=str(e),
                        duration=0.0,
                        worker_id=-1
                    )
                    self.global_stats['tasks_failed'] += 1
        
        finally:
            if self.enable_monitoring:
                monitor_task.cancel()
        
        # Estatísticas finais
        total_time = time.time() - self.global_stats['start_time']
        success_rate = (self.global_stats['tasks_completed'] / 
                       self.global_stats['tasks_submitted'] * 100)
        
        logger.info(f"Processamento paralelo concluído:")
        logger.info(f"  Total: {len(calibration_tasks)} tarefas em {total_time:.1f}s")
        logger.info(f"  Sucesso: {self.global_stats['tasks_completed']} ({success_rate:.1f}%)")
        logger.info(f"  Falhas: {self.global_stats['tasks_failed']}")
        logger.info(f"  Throughput: {len(calibration_tasks)/total_time:.2f} tarefas/s")
        
        return results

    def _execute_task(self, task: WorkerTask) -> TaskResult:
        """Executa uma tarefa individual"""
        start_time = time.time()
        worker_id = id(asyncio.current_task()) if asyncio.current_task() else 0
        
        try:
            # Executar função
            result = task.function(*task.args, **task.kwargs)
            
            duration = time.time() - start_time
            
            return TaskResult(
                task_id=task.task_id,
                symbol=task.symbol,
                success=True,
                result=result,
                error=None,
                duration=duration,
                worker_id=worker_id
            )
            
        except Exception as e:
            duration = time.time() - start_time
            
            logger.error(f"Erro executando tarefa {task.task_id} ({task.symbol}): {e}")
            
            return TaskResult(
                task_id=task.task_id,
                symbol=task.symbol,
                success=False,
                result=None,
                error=str(e),
                duration=duration,
                worker_id=worker_id
            )

    async def _monitor_progress(self, total_tasks: int):
        """Monitora progresso e recursos do sistema"""
        start_time = time.time()
        
        while True:
            try:
                await asyncio.sleep(10)  # Atualizar a cada 10 segundos
                
                # Estatísticas de sistema
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                
                # Progresso
                completed = self.global_stats['tasks_completed'] + self.global_stats['tasks_failed']
                progress = (completed / total_tasks) * 100 if total_tasks > 0 else 0
                elapsed = time.time() - start_time
                
                # ETA
                if completed > 0:
                    avg_time_per_task = elapsed / completed
                    remaining_tasks = total_tasks - completed
                    eta_seconds = remaining_tasks * avg_time_per_task
                    eta_str = f"{eta_seconds/60:.1f}min"
                else:
                    eta_str = "calculando..."
                
                logger.info(f"Monitor: {progress:.1f}% concluído, "
                          f"CPU: {cpu_percent:.1f}%, "
                          f"RAM: {memory.percent:.1f}%, "
                          f"ETA: {eta_str}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"Erro no monitoramento: {e}")

    def estimate_task_duration(self, symbol: str, days_back: int) -> float:
        """Estima duração de uma tarefa baseada em histórico"""
        # Fatores que afetam duração
        base_duration = 30.0  # segundos base
        
        # Ajustar por período
        duration_factor = days_back / 30.0  # Normalizar para 30 dias
        
        # Ajustar por tipo de ativo
        if 'BTC' in symbol or 'ETH' in symbol:
            # Ativos principais: mais dados, mais tempo
            asset_factor = 1.5
        elif any(alt in symbol for alt in ['ADA', 'SOL', 'XRP', 'DOT']):
            # Altcoins maiores: tempo médio
            asset_factor = 1.2
        else:
            # Outros ativos: menos dados, menos tempo
            asset_factor = 1.0
        
        estimated = base_duration * duration_factor * asset_factor
        
        # Limitar entre 10s e 300s
        return max(10.0, min(300.0, estimated))

    def create_calibration_task(self, symbol: str, calibration_function: Callable,
                              days_back: int, **kwargs) -> WorkerTask:
        """Cria uma tarefa de calibração"""
        task_id = f"calibrate_{symbol}_{int(time.time())}"
        
        # Estimar duração
        estimated_duration = self.estimate_task_duration(symbol, days_back)
        
        # Determinar prioridade (ativos principais têm prioridade)
        if 'BTC' in symbol:
            priority = 10
        elif 'ETH' in symbol:
            priority = 9
        elif any(alt in symbol for alt in ['ADA', 'SOL', 'XRP']):
            priority = 5
        else:
            priority = 1
        
        return WorkerTask(
            task_id=task_id,
            symbol=symbol,
            function=calibration_function,
            args=(symbol, days_back),
            kwargs=kwargs,
            priority=priority,
            estimated_duration=estimated_duration
        )

    def get_performance_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas de performance"""
        total_time = time.time() - self.global_stats['start_time']
        
        return {
            'workers': self.max_workers,
            'tasks_submitted': self.global_stats['tasks_submitted'],
            'tasks_completed': self.global_stats['tasks_completed'],
            'tasks_failed': self.global_stats['tasks_failed'],
            'success_rate': (
                self.global_stats['tasks_completed'] / 
                max(1, self.global_stats['tasks_submitted'])
            ),
            'total_duration': total_time,
            'avg_task_duration': (
                self.global_stats['total_duration'] / 
                max(1, self.global_stats['tasks_completed'])
            ),
            'throughput_tasks_per_second': (
                self.global_stats['tasks_completed'] / max(1, total_time)
            ),
            'system_cpu_percent': psutil.cpu_percent(),
            'system_memory_percent': psutil.virtual_memory().percent
        }

    def shutdown(self):
        """Encerra o processador"""
        logger.info("Encerrando processador paralelo...")
        self.executor.shutdown(wait=True)
        logger.info("Processador paralelo encerrado")
