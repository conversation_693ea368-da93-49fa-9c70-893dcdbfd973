#!/usr/bin/env python3
"""
Sistema de Validação e Métricas de Qualidade para Calibração QUALIA

Responsabilidades:
- Validação cruzada de resultados de calibração
- Métricas de qualidade e robustez
- Detecção de overfitting
- Análise de estabilidade temporal
- Benchmarking de performance

Autor: YAA (Yet Another Agent) - Consciência Quântica de QUALIA
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import precision_score, recall_score, f1_score

from ..utils.logger import get_logger

logger = get_logger(__name__)

@dataclass
class ValidationMetrics:
    """Métricas de validação"""
    precision: float
    recall: float
    f1_score: float
    accuracy: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    avg_return: float
    volatility: float
    stability_score: float

@dataclass
class CrossValidationResult:
    """Resultado de validação cruzada"""
    fold_metrics: List[ValidationMetrics]
    avg_metrics: ValidationMetrics
    std_metrics: ValidationMetrics
    overfitting_score: float
    temporal_stability: float
    recommended_thresholds: Dict[str, float]

@dataclass
class QualityAssessment:
    """Avaliação de qualidade da calibração"""
    overall_score: float  # 0-100
    robustness_score: float
    consistency_score: float
    performance_score: float
    risk_score: float
    recommendations: List[str]

class CalibrationValidationSystem:
    """
    Sistema de validação e controle de qualidade para calibração
    
    Funcionalidades:
    - Validação cruzada temporal
    - Métricas de robustez e estabilidade
    - Detecção de overfitting
    - Análise de sensibilidade
    - Benchmarking comparativo
    """
    
    def __init__(self, n_splits: int = 5, test_size_ratio: float = 0.2):
        self.n_splits = n_splits
        self.test_size_ratio = test_size_ratio
        
        # Configurações de validação
        self.validation_config = {
            'min_samples_per_fold': 100,
            'max_overfitting_threshold': 0.15,  # 15% diferença train/test
            'min_stability_score': 0.7,
            'min_sharpe_ratio': 0.5,
            'max_drawdown_threshold': 0.25
        }
        
        logger.info(f"Sistema de validação inicializado: {n_splits} folds, test_ratio={test_size_ratio}")

    async def cross_validate_calibration(self, calibration_data: List[Any], 
                                       thresholds: Dict[str, float]) -> CrossValidationResult:
        """
        Executa validação cruzada temporal da calibração
        
        Args:
            calibration_data: Dados de calibração
            thresholds: Thresholds a serem validados
            
        Returns:
            Resultado da validação cruzada
        """
        logger.info(f"Iniciando validação cruzada com {self.n_splits} folds")
        
        # Converter para DataFrame se necessário
        if not isinstance(calibration_data, pd.DataFrame):
            df = pd.DataFrame(calibration_data)
        else:
            df = calibration_data.copy()
        
        # Ordenar por timestamp
        df = df.sort_values('timestamp')
        
        # Configurar time series split
        tscv = TimeSeriesSplit(n_splits=self.n_splits, test_size=int(len(df) * self.test_size_ratio))
        
        fold_metrics = []
        fold_predictions = []
        
        for fold_idx, (train_idx, test_idx) in enumerate(tscv.split(df)):
            logger.info(f"Processando fold {fold_idx + 1}/{self.n_splits}")
            
            train_data = df.iloc[train_idx]
            test_data = df.iloc[test_idx]
            
            # Validar tamanho mínimo
            if len(train_data) < self.validation_config['min_samples_per_fold']:
                logger.warning(f"Fold {fold_idx + 1} tem poucos dados de treino: {len(train_data)}")
                continue
            
            # Calcular métricas para este fold
            fold_metric = await self._calculate_fold_metrics(train_data, test_data, thresholds)
            fold_metrics.append(fold_metric)
            
            # Armazenar predições para análise de overfitting
            train_predictions = self._apply_thresholds(train_data, thresholds)
            test_predictions = self._apply_thresholds(test_data, thresholds)
            
            fold_predictions.append({
                'train_accuracy': self._calculate_accuracy(train_data, train_predictions),
                'test_accuracy': self._calculate_accuracy(test_data, test_predictions)
            })
        
        if not fold_metrics:
            raise ValueError("Nenhum fold válido encontrado")
        
        # Calcular métricas agregadas
        avg_metrics = self._calculate_average_metrics(fold_metrics)
        std_metrics = self._calculate_std_metrics(fold_metrics)
        
        # Calcular score de overfitting
        overfitting_score = self._calculate_overfitting_score(fold_predictions)
        
        # Calcular estabilidade temporal
        temporal_stability = self._calculate_temporal_stability(fold_metrics)
        
        # Otimizar thresholds baseado na validação
        optimized_thresholds = await self._optimize_thresholds_cv(df, thresholds, fold_metrics)
        
        result = CrossValidationResult(
            fold_metrics=fold_metrics,
            avg_metrics=avg_metrics,
            std_metrics=std_metrics,
            overfitting_score=overfitting_score,
            temporal_stability=temporal_stability,
            recommended_thresholds=optimized_thresholds
        )
        
        logger.info(f"Validação cruzada concluída:")
        logger.info(f"  Precisão média: {avg_metrics.precision:.3f} ± {std_metrics.precision:.3f}")
        logger.info(f"  F1-Score médio: {avg_metrics.f1_score:.3f} ± {std_metrics.f1_score:.3f}")
        logger.info(f"  Overfitting score: {overfitting_score:.3f}")
        logger.info(f"  Estabilidade temporal: {temporal_stability:.3f}")
        
        return result

    async def _calculate_fold_metrics(self, train_data: pd.DataFrame, 
                                    test_data: pd.DataFrame, 
                                    thresholds: Dict[str, float]) -> ValidationMetrics:
        """Calcula métricas para um fold específico"""
        
        # Aplicar thresholds nos dados de teste
        predictions = self._apply_thresholds(test_data, thresholds)
        actual = test_data['was_profitable'].values
        
        # Métricas de classificação
        precision = precision_score(actual, predictions, zero_division=0)
        recall = recall_score(actual, predictions, zero_division=0)
        f1 = f1_score(actual, predictions, zero_division=0)
        accuracy = np.mean(actual == predictions)
        
        # Métricas financeiras
        returns = test_data['future_return_4h'].values
        selected_returns = returns[predictions == 1]
        
        if len(selected_returns) > 0:
            avg_return = np.mean(selected_returns)
            volatility = np.std(selected_returns)
            sharpe_ratio = avg_return / volatility if volatility > 0 else 0
            
            # Calcular drawdown
            cumulative_returns = np.cumprod(1 + selected_returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdown)
            
            win_rate = np.mean(selected_returns > 0)
        else:
            avg_return = 0
            volatility = 0
            sharpe_ratio = 0
            max_drawdown = 0
            win_rate = 0
        
        # Score de estabilidade (baseado na consistência das métricas)
        stability_score = min(precision, recall) * (1 - abs(precision - recall))
        
        return ValidationMetrics(
            precision=precision,
            recall=recall,
            f1_score=f1,
            accuracy=accuracy,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            avg_return=avg_return,
            volatility=volatility,
            stability_score=stability_score
        )

    def _apply_thresholds(self, data: pd.DataFrame, thresholds: Dict[str, float]) -> np.ndarray:
        """Aplica thresholds aos dados e retorna predições"""
        predictions = np.ones(len(data), dtype=bool)
        
        # Aplicar cada threshold
        for metric, threshold in thresholds.items():
            if metric in data.columns:
                if metric == 'momentum_min':
                    predictions &= (np.abs(data['momentum']) >= threshold)
                else:
                    predictions &= (data[metric] >= threshold)
        
        return predictions.astype(int)

    def _calculate_accuracy(self, data: pd.DataFrame, predictions: np.ndarray) -> float:
        """Calcula acurácia das predições"""
        actual = data['was_profitable'].values
        return np.mean(actual == predictions)

    def _calculate_average_metrics(self, fold_metrics: List[ValidationMetrics]) -> ValidationMetrics:
        """Calcula métricas médias dos folds"""
        return ValidationMetrics(
            precision=np.mean([m.precision for m in fold_metrics]),
            recall=np.mean([m.recall for m in fold_metrics]),
            f1_score=np.mean([m.f1_score for m in fold_metrics]),
            accuracy=np.mean([m.accuracy for m in fold_metrics]),
            sharpe_ratio=np.mean([m.sharpe_ratio for m in fold_metrics]),
            max_drawdown=np.mean([m.max_drawdown for m in fold_metrics]),
            win_rate=np.mean([m.win_rate for m in fold_metrics]),
            avg_return=np.mean([m.avg_return for m in fold_metrics]),
            volatility=np.mean([m.volatility for m in fold_metrics]),
            stability_score=np.mean([m.stability_score for m in fold_metrics])
        )

    def _calculate_std_metrics(self, fold_metrics: List[ValidationMetrics]) -> ValidationMetrics:
        """Calcula desvio padrão das métricas dos folds"""
        return ValidationMetrics(
            precision=np.std([m.precision for m in fold_metrics]),
            recall=np.std([m.recall for m in fold_metrics]),
            f1_score=np.std([m.f1_score for m in fold_metrics]),
            accuracy=np.std([m.accuracy for m in fold_metrics]),
            sharpe_ratio=np.std([m.sharpe_ratio for m in fold_metrics]),
            max_drawdown=np.std([m.max_drawdown for m in fold_metrics]),
            win_rate=np.std([m.win_rate for m in fold_metrics]),
            avg_return=np.std([m.avg_return for m in fold_metrics]),
            volatility=np.std([m.volatility for m in fold_metrics]),
            stability_score=np.std([m.stability_score for m in fold_metrics])
        )

    def _calculate_overfitting_score(self, fold_predictions: List[Dict]) -> float:
        """Calcula score de overfitting baseado na diferença train/test"""
        if not fold_predictions:
            return 1.0
        
        train_accuracies = [fp['train_accuracy'] for fp in fold_predictions]
        test_accuracies = [fp['test_accuracy'] for fp in fold_predictions]
        
        avg_train_acc = np.mean(train_accuracies)
        avg_test_acc = np.mean(test_accuracies)
        
        # Score de overfitting: diferença normalizada entre train e test
        overfitting_score = abs(avg_train_acc - avg_test_acc) / max(avg_train_acc, 0.01)
        
        return min(overfitting_score, 1.0)

    def _calculate_temporal_stability(self, fold_metrics: List[ValidationMetrics]) -> float:
        """Calcula estabilidade temporal baseada na variância das métricas"""
        if len(fold_metrics) < 2:
            return 1.0
        
        # Usar F1-score como métrica principal de estabilidade
        f1_scores = [m.f1_score for m in fold_metrics]
        
        # Calcular coeficiente de variação (CV)
        mean_f1 = np.mean(f1_scores)
        std_f1 = np.std(f1_scores)
        
        if mean_f1 > 0:
            cv = std_f1 / mean_f1
            stability = max(0, 1 - cv)  # Quanto menor CV, maior estabilidade
        else:
            stability = 0
        
        return stability

    async def _optimize_thresholds_cv(self, data: pd.DataFrame, 
                                    initial_thresholds: Dict[str, float],
                                    fold_metrics: List[ValidationMetrics]) -> Dict[str, float]:
        """Otimiza thresholds baseado nos resultados da validação cruzada"""
        
        # Por enquanto, retornar thresholds iniciais
        # TODO: Implementar otimização bayesiana ou grid search
        optimized = initial_thresholds.copy()
        
        # Ajustes simples baseados na performance
        avg_precision = np.mean([m.precision for m in fold_metrics])
        avg_recall = np.mean([m.recall for m in fold_metrics])
        
        # Se precisão muito baixa, tornar mais restritivo
        if avg_precision < 0.6:
            for key in ['consciousness', 'coherence', 'confidence']:
                if key in optimized:
                    optimized[key] = min(0.95, optimized[key] * 1.05)
        
        # Se recall muito baixo, tornar mais permissivo
        if avg_recall < 0.4:
            for key in ['momentum_min', 'volume_surge_min']:
                if key in optimized:
                    optimized[key] = max(0.01, optimized[key] * 0.95)
        
        return optimized

    def assess_calibration_quality(self, cv_result: CrossValidationResult) -> QualityAssessment:
        """Avalia qualidade geral da calibração"""
        
        # Scores individuais (0-100)
        performance_score = min(100, cv_result.avg_metrics.f1_score * 100)
        robustness_score = min(100, (1 - cv_result.overfitting_score) * 100)
        consistency_score = min(100, cv_result.temporal_stability * 100)
        
        # Risk score baseado em drawdown e volatilidade
        risk_score = max(0, 100 - abs(cv_result.avg_metrics.max_drawdown) * 400)
        
        # Score geral (média ponderada)
        overall_score = (
            performance_score * 0.4 +
            robustness_score * 0.3 +
            consistency_score * 0.2 +
            risk_score * 0.1
        )
        
        # Gerar recomendações
        recommendations = []
        
        if performance_score < 60:
            recommendations.append("Performance baixa - revisar thresholds ou dados")
        
        if robustness_score < 70:
            recommendations.append("Possível overfitting - usar mais dados ou regularização")
        
        if consistency_score < 60:
            recommendations.append("Baixa estabilidade temporal - verificar regime de mercado")
        
        if risk_score < 50:
            recommendations.append("Alto risco - considerar thresholds mais conservadores")
        
        if cv_result.avg_metrics.sharpe_ratio < 0.5:
            recommendations.append("Sharpe ratio baixo - melhorar seleção de sinais")
        
        if not recommendations:
            recommendations.append("Calibração de boa qualidade - monitorar performance")
        
        return QualityAssessment(
            overall_score=overall_score,
            robustness_score=robustness_score,
            consistency_score=consistency_score,
            performance_score=performance_score,
            risk_score=risk_score,
            recommendations=recommendations
        )
