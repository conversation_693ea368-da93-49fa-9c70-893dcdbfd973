#!/usr/bin/env python3
"""
Teste para verificar se o erro do atributo 'momentum' foi corrigido.
"""

import sys
import os
sys.path.append('src')

def test_threshold_config():
    """Testa se ThresholdConfig funciona corretamente"""
    try:
        from qualia.adaptive_threshold_system import ThresholdConfig
        
        # Criar uma instância de ThresholdConfig
        config = ThresholdConfig(
            consciousness=0.8,
            coherence=0.7,
            confidence=0.6,
            volume_surge_min=0.9,
            momentum_min=0.05
        )
        
        print("✓ ThresholdConfig criado com sucesso")
        print(f"  consciousness: {config.consciousness}")
        print(f"  coherence: {config.coherence}")
        print(f"  confidence: {config.confidence}")
        print(f"  volume_surge_min: {config.volume_surge_min}")
        print(f"  momentum_min: {config.momentum_min}")
        
        # Testar acesso aos atributos
        assert hasattr(config, 'momentum_min'), "Atributo momentum_min não encontrado"
        assert not hasattr(config, 'momentum'), "Atributo momentum não deveria existir"
        
        print("✓ Atributos corretos verificados")
        return True
        
    except Exception as e:
        print(f"✗ Erro no teste ThresholdConfig: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adaptive_manager():
    """Testa se AdaptiveThresholdManager funciona sem erro de momentum"""
    try:
        from qualia.config_manager import get_config_manager
        from qualia.adaptive_threshold_system import AdaptiveThresholdManager
        
        config_manager = get_config_manager()
        manager = AdaptiveThresholdManager(config_manager)
        
        print("✓ AdaptiveThresholdManager inicializado com sucesso")
        print(f"  current_thresholds: {manager.current_thresholds}")
        
        # Testar se o atributo momentum_min existe
        assert hasattr(manager.current_thresholds, 'momentum_min'), "momentum_min não encontrado"
        
        print("✓ Atributo momentum_min verificado no manager")
        return True
        
    except Exception as e:
        print(f"✗ Erro no teste AdaptiveThresholdManager: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_tuning():
    """Testa se o auto-tuning funciona sem erro de momentum"""
    try:
        from qualia.config_manager import get_config_manager
        from qualia.adaptive_threshold_system import AdaptiveThresholdManager
        
        config_manager = get_config_manager()
        manager = AdaptiveThresholdManager(config_manager)
        
        # Adicionar algumas métricas de teste para evitar erro de dados insuficientes
        manager.metric_statistics = {
            'consciousness': [0.8, 0.7, 0.9, 0.6, 0.85],
            'coherence': [0.75, 0.8, 0.7, 0.9, 0.65],
            'confidence': [0.7, 0.8, 0.75, 0.85, 0.6],
            'volume_surge': [0.9, 0.8, 0.95, 0.7, 0.85],
            'momentum': [0.05, 0.08, 0.03, 0.1, 0.06]
        }
        
        # Testar auto-tuning que estava causando o erro
        result = manager.auto_tune_all_thresholds()
        
        print("✓ Auto-tuning executado com sucesso")
        print(f"  Resultado: {type(result)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Erro no teste auto-tuning: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== TESTE DE CORREÇÃO DO ERRO 'momentum' ===\n")
    
    tests = [
        ("ThresholdConfig", test_threshold_config),
        ("AdaptiveThresholdManager", test_adaptive_manager),
        ("Auto-tuning", test_auto_tuning)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- Teste: {test_name} ---")
        if test_func():
            passed += 1
        print()
    
    print("=== RESUMO ===")
    print(f"Testes passaram: {passed}/{total}")
    
    if passed == total:
        print("✓ TODOS OS TESTES PASSARAM - Erro corrigido com sucesso!")
        sys.exit(0)
    else:
        print("✗ ALGUNS TESTES FALHARAM - Erro ainda presente")
        sys.exit(1)
